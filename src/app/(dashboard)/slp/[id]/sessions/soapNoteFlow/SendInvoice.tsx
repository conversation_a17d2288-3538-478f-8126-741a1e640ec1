import { useSendInvoiceMail } from '@/app/(dashboard)/contacts/[id]/email/useSendInvoiceMail';
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { useInvoiceHook } from '@/hooks/slp/soapNoteFlow/useInvoiceHook';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import moment from 'moment';
import { useMemo } from 'react';
import { FaFilePdf } from 'react-icons/fa6';
import { FiChevronDown, FiChevronUp, FiSend } from 'react-icons/fi';

interface SendInvoiceProp {
  soapNoteHook: TCreateInvoiceHook | undefined;
  booking: any;
  onScrollToPdf?: () => void;
}
export default function SendInvoice({
  booking,
  soapNoteHook,
  onScrollToPdf,
}: SendInvoiceProp) {
  const {
    toggleSendInvoiceForm,
    isSendInoiceOpen,
    onEmailSuccess,
    onCloseSendInvoice,
    markAsSent,
    updateInvoiceLoading,
  } = useInvoiceHook({
    soapNoteHook: soapNoteHook,
    booking,
  });
  const invoice = booking?.slp_notes?.invoice;

  const {
    // slpEmailOptions,
    form,
    setForm,
    sendEmail,
    loading,
    // tomorrow,
  } = useSendInvoiceMail({
    data: { ...invoice, clients: booking?.clients },
    onCloseModal: onEmailSuccess,
    invoice,
  });

  const showDropDown = useMemo(() => {
    return (
      booking?.clients &&
      booking?.clients?.client_emails &&
      booking?.clients?.client_emails?.length &&
      booking?.clients?.client_emails?.length > 1
    );
  }, [booking]);

  const clientEmailsOptions = useMemo(() => {
    return booking?.clients?.client_emails?.map((item: any) => ({
      label: item.email,
      value: item.email,
    }));
  }, [booking]);

  return (
    <Box
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      py={'1.5rem'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      maxH={'fit-content'}
    >
      <Box>
        <Box
          display={'grid'}
          gridTemplateColumns={{ base: '1fr', md: '1fr auto' }}
          width={'full'}
          maxWidth={'full'}
          gap={'1.5rem'}
          overflow={'hidden'}
          justifyContent={'space-between'}
          // border={'2px solid red'}
        >
          <Box display={'flex'} alignItems={'center'} gap={'1.25rem'}>
            <Box
              rounded={'full'}
              fontSize={'18px'}
              display={'flex'}
              justifyContent={'center'}
              alignItems={'center'}
              minW={'36px'}
              w={'36px'}
              h={'36px'}
              maxW={'36px'}
              cursor={'pointer'}
              color={'#E97A5B'}
              border={'2px solid #E97A5B'}
            >
              {' '}
              <FiSend />
            </Box>
            <Box>
              <Text fontSize={{ base: 'sm', md: 'md' }} color={'GrayText'}>
                Send Invoice
              </Text>

              {booking?.slp_notes?.invoice?.sent_at ? (
                <Text fontSize={{ base: 'md', md: 'base' }}>
                  <b>Last Sent:</b>{' '}
                  {moment(booking?.slp_notes?.invoice?.sent_at)?.format(
                    ' MMMM D, YYYY'
                  )}
                </Text>
              ) : null}
            </Box>
          </Box>
          <Box
            w={'full'}
            h={'fit'}
            display={'flex'}
            justifyContent={{ mdDown: 'end' }}
            // gridTemplateColumns={{ base: '1fr', lg: 'auto auto' }}
            gap={'0.5rem'}
          >
            {!booking?.slp_notes?.invoice?.sent_at ? (
              <Button
                rounded={'2xl'}
                fontSize={'14px'}
                bg={'transparent'}
                color={'black'}
                onClick={markAsSent}
                loading={updateInvoiceLoading}
                disabled={
                  updateInvoiceLoading || !booking?.slp_notes?.invoice?.id
                }
                border={'2px solid black'}
              >
                {/* !booking?.slp_notes?.invoice || booking?.slp_notes?.invoice?.sent_at */}
                {booking?.slp_notes?.invoice?.sent_at ? 'Sent' : 'Mark as sent'}
              </Button>
            ) : null}
            <Button
              rounded={'2xl'}
              fontSize={'14px'}
              bg={'#E97A5B'}
              color={'white'}
              title={
                !booking?.slp_notes?.invoice
                  ? 'Please Create Invoice First'
                  : ''
              }
              disabled={!booking?.slp_notes?.invoice?.id}
              // border={'2px solid black'}
              onClick={toggleSendInvoiceForm}
            >
              {booking?.slp_notes?.invoice?.sent_at
                ? 'Resend Invoice'
                : 'Send Invoice'}
              {isSendInoiceOpen ? (
                <FiChevronUp color="white" />
              ) : (
                <FiChevronDown color="white" />
              )}
            </Button>
          </Box>
        </Box>

        <Box maxH={isSendInoiceOpen ? 'fit-content' : 0} overflow={'hidden'}>
          <Box h={'100%'}>
            <Stack gap={' 0.5rem'} my={'0.5rem'} h={'fit'}>
              {/* <CustomSelect
             label="To"
             options={clientEmailsOptions || []}
             onChange={(e) => setForm({ ...form, to: e.value })}
           /> */}
              {showDropDown ? (
                <CustomSelect
                  label="To"
                  options={clientEmailsOptions || []}
                  defaultValue={clientEmailsOptions[0]}
                  onChange={(e) => setForm({ ...form, to: e.value })}
                />
              ) : (
                <StringInput
                  inputProps={{
                    name: 'to',
                    value: String(form?.to),
                    disabled: true,
                  }}
                  fieldProps={{
                    label: 'To',
                  }}
                />
              )}

              <StringInput
                inputProps={{
                  name: 'from',
                  // value: '<EMAIL>',
                  value: String(form?.from),
                  disabled: true,
                }}
                fieldProps={{
                  label: 'From',
                }}
              />
              <StringInput
                inputProps={{
                  name: 'cc',
                  value: String(form?.cc),
                  onChange: (e) => setForm({ ...form, cc: e.target.value }),
                }}
                fieldProps={{
                  label: 'CC',
                }}
              />
              {/* <CustomSelect
             label="From"
             options={slpEmailOptions || []}
             onChange={(e) => setForm({ ...form, from: e.value })}
           /> */}
              <StringInput
                inputProps={{
                  name: 'subject',
                  value: String(form?.subject),
                  onChange: (e) =>
                    setForm({ ...form, subject: e.target.value }),
                }}
                fieldProps={{
                  label: 'Subject',
                }}
              />
              <CustomTextArea
                inputProps={{
                  name: 'body',
                  value: String(form?.body),
                  onChange: (e) => setForm({ ...form, body: e.target.value }),
                }}
                fieldProps={{
                  label: 'Body',
                }}
              />
              {/* <Checkbox
             fontWeight={500}
             colorScheme="primary"
             checked={form.schedule}
             onChange={() => setForm({ ...form, schedule: !form.schedule })}
           >
             Schedule For Later
           </Checkbox> */}
              {form.schedule && (
                <Box position={'relative'} zIndex={10}>
                  <Text fontWeight={600}>Pick Date</Text>
                  <CustomDatePicker
                    onChange={(e) => {
                      setForm({ ...form, scheduledTime: e });
                    }}
                    showTime={true}
                    // minDate={tomorrow}
                  />
                </Box>
              )}
            </Stack>

            {/* PDF attachment icon */}
            <Box my={'1rem'}>
              <Button
                rounded={'2xl'}
                fontSize={'14px'}
                border={'2px solid gray'}
                bg={'transparent'}
                onClick={() => {
                  if (onScrollToPdf) {
                    onScrollToPdf();
                  }
                }}
              >
                <FaFilePdf color="red" />
                <Text
                  color={'gray'}
                >{`${moment(invoice?.invoice_date).format('YYYY_MM_DD')}_${invoice?.name?.replace(/\s+/g, '_')}_invoice`}</Text>
              </Button>
            </Box>

            <Flex
              mt={'.5rem'}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Button
                rounded={'2xl'}
                variant={'outline'}
                onClick={onCloseSendInvoice}
              >
                Cancel
              </Button>
              <Button
                rounded={'2xl'}
                onClick={sendEmail}
                loading={loading}
                bg={'primary.500'}
              >
                Send email
              </Button>
            </Flex>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
